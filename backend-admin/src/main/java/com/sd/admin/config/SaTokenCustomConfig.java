package com.sd.admin.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.dao.SaTokenDao;
import cn.dev33.satoken.dao.SaTokenDaoRedisJackson;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenCustomConfig implements WebMvcConfigurer {

    /**
     * 注册 Sa-Token 拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 登录校验 -- 拦截所有路由，并排除登录和公开接口
            SaRouter.match("/**")
                    // 排除登录接口
                    .notMatch("/auth/login")
                    // 排除静态资源
                    .notMatch("/favicon.ico", "/error")
                    // 排除Swagger相关
                    .notMatch("/doc.html", "/webjars/**", "/swagger-resources/**", "/v3/api-docs/**")
                    // 排除Druid监控
                    .notMatch("/druid/**")
                    // 排除监控端点
                    .notMatch("/actuator/**")
                    // 执行认证函数
                    .check(r -> StpUtil.checkLogin());
            })).addPathPatterns("/**");
    }

    /**
     * Sa-Token 参数配置
     */
    @Bean
    @Primary
    public SaTokenConfig getSaTokenConfigPrimary() {
        SaTokenConfig config = new SaTokenConfig();
        // token名称 (同时也是cookie名称)
        config.setTokenName("Authorization");
        // token前缀
        config.setTokenPrefix("Bearer");
        // token有效期，单位秒，默认30天
        config.setTimeout(24 * 60 * 60);
        // token临时有效期 (指定时间内无操作就过期)，单位秒
        config.setActivityTimeout(30 * 60);
        // 是否允许同一账号并发登录 (为true时允许一个账号多地同时登录)
        config.setIsConcurrent(true);
        // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
        config.setIsShare(false);
        // token风格
        config.setTokenStyle("uuid");
        // 是否输出操作日志
        config.setIsLog(true);
        // 是否尝试从请求体里读取token
        config.setIsReadBody(false);
        // 是否尝试从header里读取token
        config.setIsReadHeader(true);
        // 是否尝试从cookie里读取token
        config.setIsReadCookie(false);
        return config;
    }
}
