package com.sd.admin.service;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.admin.dto.LoginDTO;
import com.sd.admin.dto.SysUserCreateDTO;
import com.sd.admin.dto.SysUserPasswordResetDTO;
import com.sd.admin.dto.SysUserQueryDTO;
import com.sd.admin.dto.SysUserUpdateDTO;
import com.sd.admin.entity.SysPermission;
import com.sd.admin.entity.SysRole;
import com.sd.admin.entity.SysUser;
import com.sd.admin.mapper.SysUserMapper;
import com.sd.admin.vo.CurrentUserVO;
import com.sd.admin.vo.LoginUserVO;
import com.sd.admin.vo.LoginVO;

import com.sd.admin.vo.SimpleRoleVO;
import com.sd.admin.vo.SysPermissionVO;
import com.sd.admin.vo.SysRoleVO;
import com.sd.admin.vo.SysUserVO;
import com.sd.common.api.PageResult;
import com.sd.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统用户服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysUserService extends ServiceImpl<SysUserMapper, SysUser> {

    private final PasswordEncoder passwordEncoder;
    private final SysRoleService sysRoleService;
    private final SysUserRoleService sysUserRoleService;
    private final SysPermissionService sysPermissionService;

    /**
     * 分页查询用户列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public PageResult<SysUserVO> page(SysUserQueryDTO queryDTO) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(queryDTO.getUsername()), SysUser::getUsername, queryDTO.getUsername())
               .like(StringUtils.hasText(queryDTO.getNickname()), SysUser::getNickname, queryDTO.getNickname())
               .like(StringUtils.hasText(queryDTO.getEmail()), SysUser::getEmail, queryDTO.getEmail())
               .like(StringUtils.hasText(queryDTO.getPhone()), SysUser::getPhone, queryDTO.getPhone())
               .eq(queryDTO.getStatus() != null, SysUser::getStatus, queryDTO.getStatus())
               .orderByDesc(SysUser::getCreateTime);

        Page<SysUser> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        Page<SysUser> result = this.page(page, wrapper);

        List<SysUserVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(voList, result.getTotal(), queryDTO.getCurrent(), queryDTO.getPageSize());
    }

    /**
     * 根据ID查询用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    @Cacheable(value = "user", key = "#id")
    public SysUserVO getDetail(Long id) {
        SysUser user = this.getById(id);
        Assert.notNull(user, "用户不存在");
        return convertToVO(user);
    }

    /**
     * 创建用户
     *
     * @param createDTO 创建参数
     * @return 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(SysUserCreateDTO createDTO) {
        // 检查用户名是否重复
        checkUsernameUnique(createDTO.getUsername(), null);
        // 检查邮箱是否重复
        if (StringUtils.hasText(createDTO.getEmail())) {
            checkEmailUnique(createDTO.getEmail(), null);
        }
        // 检查手机号是否重复
        if (StringUtils.hasText(createDTO.getPhone())) {
            checkPhoneUnique(createDTO.getPhone(), null);
        }

        SysUser user = new SysUser();
        BeanUtils.copyProperties(createDTO, user);
        // 加密密码
        user.setPassword(passwordEncoder.encode(createDTO.getPassword()));
        this.save(user);

        // 分配角色
        if (!CollectionUtils.isEmpty(createDTO.getRoleIds())) {
            sysUserRoleService.assignUserRoles(user.getId(), createDTO.getRoleIds());
        }

        return user.getId();
    }

    /**
     * 更新用户
     *
     * @param updateDTO 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user", key = "#updateDTO.id")
    public void update(SysUserUpdateDTO updateDTO) {
        SysUser existUser = this.getById(updateDTO.getId());
        Assert.notNull(existUser, "用户不存在");

        // 检查用户名是否重复
        checkUsernameUnique(updateDTO.getUsername(), updateDTO.getId());
        // 检查邮箱是否重复
        if (StringUtils.hasText(updateDTO.getEmail())) {
            checkEmailUnique(updateDTO.getEmail(), updateDTO.getId());
        }
        // 检查手机号是否重复
        if (StringUtils.hasText(updateDTO.getPhone())) {
            checkPhoneUnique(updateDTO.getPhone(), updateDTO.getId());
        }

        SysUser user = new SysUser();
        BeanUtils.copyProperties(updateDTO, user);
        this.updateById(user);

        // 重新分配角色
        sysUserRoleService.deleteByUserId(user.getId());
        if (!CollectionUtils.isEmpty(updateDTO.getRoleIds())) {
            sysUserRoleService.assignUserRoles(user.getId(), updateDTO.getRoleIds());
        }
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user", key = "#id")
    public void delete(Long id) {
        SysUser user = this.getById(id);
        Assert.notNull(user, "用户不存在");

        // 删除用户
        this.removeById(id);
        // 删除用户角色关联
        sysUserRoleService.deleteByUserId(id);
    }

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user", allEntries = true)
    public void batchDelete(List<Long> ids) {
        Assert.notEmpty(ids, "用户ID列表不能为空");

        // 删除用户
        this.removeByIds(ids);
        // 删除用户角色关联
        ids.forEach(sysUserRoleService::deleteByUserId);
    }

    /**
     * 重置用户密码
     *
     * @param resetDTO 重置参数
     */
    @CacheEvict(value = "user", key = "#resetDTO.id")
    public void resetPassword(SysUserPasswordResetDTO resetDTO) {
        SysUser user = this.getById(resetDTO.getId());
        Assert.notNull(user, "用户不存在");

        user.setPassword(passwordEncoder.encode(resetDTO.getNewPassword()));
        this.updateById(user);
    }

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户
     */
    public SysUser getUserByUsername(String username) {
        return baseMapper.selectByUsername(username);
    }

    /**
     * 管理员登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    public LoginVO login(LoginDTO loginDTO) {
        // 根据用户名查询用户
        SysUser user = getUserByUsername(loginDTO.getUsername());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            //throw new BusinessException("密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != 0) {
            throw new BusinessException("用户已被停用");
        }

        // 登录成功，生成token
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        // 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setToken(token);
        loginVO.setUser(convertToLoginUserVO(user));

        return loginVO;
    }

    /**
     * 检查用户名是否唯一
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     */
    private void checkUsernameUnique(String username, Long excludeId) {
        int count = baseMapper.checkUsernameUnique(username, excludeId);
        if (count > 0) {
            throw new BusinessException("用户名已存在");
        }
    }

    /**
     * 检查邮箱是否唯一
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     */
    private void checkEmailUnique(String email, Long excludeId) {
        int count = baseMapper.checkEmailUnique(email, excludeId);
        if (count > 0) {
            throw new BusinessException("邮箱已存在");
        }
    }

    /**
     * 检查手机号是否唯一
     *
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     */
    private void checkPhoneUnique(String phone, Long excludeId) {
        int count = baseMapper.checkPhoneUnique(phone, excludeId);
        if (count > 0) {
            throw new BusinessException("手机号已存在");
        }
    }

    /**
     * 转换为VO
     *
     * @param user 用户实体
     * @return 用户VO
     */
    private SysUserVO convertToVO(SysUser user) {
        SysUserVO vo = new SysUserVO();
        BeanUtils.copyProperties(user, vo);

        // 查询用户角色
        List<SysRole> roles = sysRoleService.getRolesByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roles)) {
            List<SysRoleVO> roleVOs = roles.stream()
                    .map(role -> {
                        SysRoleVO roleVO = new SysRoleVO();
                        BeanUtils.copyProperties(role, roleVO);

                        // 查询角色权限
                        List<SysPermission> permissions = sysPermissionService.getPermissionsByRoleId(role.getId());
                        if (!CollectionUtils.isEmpty(permissions)) {
                            List<SysPermissionVO> permissionVOs = permissions.stream()
                                    .map(permission -> {
                                        SysPermissionVO permissionVO = new SysPermissionVO();
                                        BeanUtils.copyProperties(permission, permissionVO);
                                        return permissionVO;
                                    })
                                    .collect(Collectors.toList());
                            roleVO.setPermissions(permissionVOs);
                        }

                        return roleVO;
                    })
                    .collect(Collectors.toList());
            vo.setRoles(roleVOs);

            List<String> roleNames = roles.stream()
                    .map(SysRole::getRoleName)
                    .collect(Collectors.toList());
            vo.setRoleNames(roleNames);
        }

        return vo;
    }

    /**
     * 转换为登录用户VO（精简版）
     *
     * @param user 用户实体
     * @return 登录用户VO
     */
    private LoginUserVO convertToLoginUserVO(SysUser user) {
        LoginUserVO vo = new LoginUserVO();
        vo.setId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setNickname(user.getNickname());
        vo.setAvatar(user.getAvatar());
        return vo;
    }

    /**
     * 获取当前用户信息（精简版）
     *
     * @param userId 用户ID
     * @return 当前用户信息
     */
    public CurrentUserVO getCurrentUserInfo(Long userId) {
        SysUser user = this.getById(userId);
        Assert.notNull(user, "用户不存在");
        return convertToCurrentUserVO(user);
    }

    /**
     * 转换为当前用户VO（精简版）
     *
     * @param user 用户实体
     * @return 当前用户VO
     */
    private CurrentUserVO convertToCurrentUserVO(SysUser user) {
        CurrentUserVO vo = new CurrentUserVO();
        vo.setId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setNickname(user.getNickname());
        vo.setEmail(user.getEmail());
        vo.setAvatar(user.getAvatar());

        // 查询用户角色（只包含权限标识）
        List<SysRole> roles = sysRoleService.getRolesByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roles)) {
            List<SimpleRoleVO> roleVOs = roles.stream()
                    .map(role -> {
                        SimpleRoleVO roleVO = new SimpleRoleVO();
                        roleVO.setId(role.getId());
                        roleVO.setRoleName(role.getRoleName());
                        roleVO.setRoleKey(role.getRoleKey());

                        // 查询角色权限标识
                        List<SysPermission> permissions = sysPermissionService.getPermissionsByRoleId(role.getId());
                        if (!CollectionUtils.isEmpty(permissions)) {
                            List<String> permissionKeys = permissions.stream()
                                    .map(SysPermission::getPermissionKey)
                                    .filter(key -> key != null && !key.isEmpty())
                                    .collect(Collectors.toList());
                            roleVO.setPermissions(permissionKeys);
                        }

                        return roleVO;
                    })
                    .collect(Collectors.toList());
            vo.setRoles(roleVOs);
        }

        return vo;
    }
}
