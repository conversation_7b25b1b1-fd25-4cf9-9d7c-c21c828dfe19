package com.sd.admin.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.admin.dto.SysRoleCreateDTO;
import com.sd.admin.dto.SysRoleQueryDTO;
import com.sd.admin.dto.SysRoleUpdateDTO;
import com.sd.admin.entity.SysPermission;
import com.sd.admin.entity.SysRole;
import com.sd.admin.entity.SysRolePermission;
import com.sd.admin.mapper.SysRoleMapper;
import com.sd.admin.mapper.SysRolePermissionMapper;
import com.sd.admin.vo.SysPermissionVO;
import com.sd.admin.vo.SysRoleVO;
import com.sd.common.api.PageResult;
import com.sd.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统角色服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysRoleService extends ServiceImpl<SysRoleMapper, SysRole> {

    private final SysRolePermissionMapper sysRolePermissionMapper;
    private final SysPermissionService sysPermissionService;

    /**
     * 分页查询角色列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public PageResult<SysRoleVO> page(SysRoleQueryDTO queryDTO) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(queryDTO.getRoleName() != null, SysRole::getRoleName, queryDTO.getRoleName())
               .like(queryDTO.getRoleKey() != null, SysRole::getRoleKey, queryDTO.getRoleKey())
               .eq(queryDTO.getStatus() != null, SysRole::getStatus, queryDTO.getStatus())
               .orderByAsc(SysRole::getSort);

        Page<SysRole> page = new Page<>(queryDTO.getCurrent(), queryDTO.getPageSize());
        Page<SysRole> result = this.page(page, wrapper);

        List<SysRoleVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageResult<>(voList, result.getTotal(), queryDTO.getCurrent(), queryDTO.getPageSize());
    }

    /**
     * 根据ID查询角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    @Cacheable(value = "role", key = "#id")
    public SysRoleVO getDetail(Long id) {
        SysRole role = this.getById(id);
        Assert.notNull(role, "角色不存在");
        return convertToVO(role);
    }

    /**
     * 创建角色
     *
     * @param createDTO 创建参数
     * @return 角色ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(SysRoleCreateDTO createDTO) {
        // 检查角色名称是否重复
        checkRoleNameUnique(createDTO.getRoleName(), null);
        // 检查角色标识是否重复
        checkRoleKeyUnique(createDTO.getRoleKey(), null);

        SysRole role = new SysRole();
        BeanUtils.copyProperties(createDTO, role);
        this.save(role);

        // 分配权限
        if (!CollectionUtils.isEmpty(createDTO.getPermissionIds())) {
            assignPermissions(role.getId(), createDTO.getPermissionIds());
        }

        return role.getId();
    }

    /**
     * 更新角色
     *
     * @param updateDTO 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", key = "#updateDTO.id")
    public void update(SysRoleUpdateDTO updateDTO) {
        SysRole existRole = this.getById(updateDTO.getId());
        Assert.notNull(existRole, "角色不存在");

        // 检查角色名称是否重复
        checkRoleNameUnique(updateDTO.getRoleName(), updateDTO.getId());
        // 检查角色标识是否重复
        checkRoleKeyUnique(updateDTO.getRoleKey(), updateDTO.getId());

        SysRole role = new SysRole();
        BeanUtils.copyProperties(updateDTO, role);
        this.updateById(role);

        // 重新分配权限
        sysRolePermissionMapper.deleteByRoleId(role.getId());
        if (!CollectionUtils.isEmpty(updateDTO.getPermissionIds())) {
            assignPermissions(role.getId(), updateDTO.getPermissionIds());
        }
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", key = "#id")
    public void delete(Long id) {
        SysRole role = this.getById(id);
        Assert.notNull(role, "角色不存在");

        // 删除角色
        this.removeById(id);
        // 删除角色权限关联
        sysRolePermissionMapper.deleteByRoleId(id);
    }

    /**
     * 批量删除角色
     *
     * @param ids 角色ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public void batchDelete(List<Long> ids) {
        Assert.notEmpty(ids, "角色ID列表不能为空");

        // 删除角色
        this.removeByIds(ids);
        // 删除角色权限关联
        ids.forEach(sysRolePermissionMapper::deleteByRoleId);
    }

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<SysRole> getRolesByUserId(Long userId) {
        return baseMapper.selectRolesByUserId(userId);
    }

    /**
     * 查询所有角色列表
     *
     * @return 角色列表
     */
    public List<SysRoleVO> listAll() {
        List<SysRole> roles = this.list(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getStatus, 0)
                .orderByAsc(SysRole::getSort));
        return roles.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 分配权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    private void assignPermissions(Long roleId, List<Long> permissionIds) {
        List<SysRolePermission> sysRolePermissions = permissionIds.stream()
                .map(permissionId -> {
                    SysRolePermission sysRolePermission = new SysRolePermission();
                    sysRolePermission.setRoleId(roleId);
                    sysRolePermission.setPermissionId(permissionId);
                    return sysRolePermission;
                })
                .collect(Collectors.toList());
        sysRolePermissionMapper.batchInsert(sysRolePermissions);
    }

    /**
     * 检查角色名称是否唯一
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     */
    private void checkRoleNameUnique(String roleName, Long excludeId) {
        int count = baseMapper.checkRoleNameUnique(roleName, excludeId);
        if (count > 0) {
            throw new BusinessException("角色名称已存在");
        }
    }

    /**
     * 检查角色标识是否唯一
     *
     * @param roleKey 角色标识
     * @param excludeId 排除的角色ID
     */
    private void checkRoleKeyUnique(String roleKey, Long excludeId) {
        int count = baseMapper.checkRoleKeyUnique(roleKey, excludeId);
        if (count > 0) {
            throw new BusinessException("角色标识已存在");
        }
    }

    /**
     * 转换为VO
     *
     * @param role 角色实体
     * @return 角色VO
     */
    private SysRoleVO convertToVO(SysRole role) {
        SysRoleVO vo = new SysRoleVO();
        BeanUtils.copyProperties(role, vo);

        // 查询角色权限
        List<SysPermission> permissions = sysPermissionService.getPermissionsByRoleId(role.getId());
        if (!CollectionUtils.isEmpty(permissions)) {
            List<SysPermissionVO> permissionVOs = permissions.stream()
                    .map(permission -> {
                        SysPermissionVO permissionVO = new SysPermissionVO();
                        BeanUtils.copyProperties(permission, permissionVO);
                        return permissionVO;
                    })
                    .collect(Collectors.toList());
            vo.setPermissions(permissionVOs);
        }

        return vo;
    }
}
