package com.sd.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.sd.admin.dto.AppUserPasswordResetDTO;
import com.sd.admin.dto.AppUserQueryDTO;
import com.sd.admin.service.AppUserManageService;
import com.sd.admin.vo.AppUserVO;
import com.sd.common.api.PageResult;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户端用户管理控制器（管理后台）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app-user")
@RequiredArgsConstructor
@Tag(name = "用户端用户管理", description = "管理后台管理用户端用户相关接口")
public class AppUserController {

    private final AppUserManageService appUserManageService;

    /**
     * 分页查询用户端用户列表
     */
    @GetMapping("/page")
    @SaCheckPermission("app:user:view")
    @Operation(summary = "分页查询用户端用户列表")
    public R<PageResult<AppUserVO>> page(@Valid AppUserQueryDTO queryDTO) {
        PageResult<AppUserVO> result = appUserManageService.page(queryDTO);
        return R.ok(result);
    }

    /**
     * 根据ID查询用户端用户详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission("app:user:view")
    @Operation(summary = "根据ID查询用户端用户详情")
    @Parameter(name = "id", description = "用户ID", required = true)
    public R<AppUserVO> getDetail(@PathVariable Long id) {
        AppUserVO result = appUserManageService.getDetail(id);
        return R.ok(result);
    }

    /**
     * 重置用户登录密码
     */
    @PutMapping("/reset-password")
    @SaCheckPermission("app:user:reset:password")
    @Operation(summary = "重置用户登录密码")
    public R<Void> resetPassword(@Valid @RequestBody AppUserPasswordResetDTO resetDTO) {
        appUserManageService.resetPassword(resetDTO);
        return R.ok();
    }

    /**
     * 重置用户支付密码
     */
    @PutMapping("/reset-pay-password")
    @SaCheckPermission("app:user:reset:pay:password")
    @Operation(summary = "重置用户支付密码")
    public R<Void> resetPayPassword(@Valid @RequestBody AppUserPasswordResetDTO resetDTO) {
        appUserManageService.resetPayPassword(resetDTO);
        return R.ok();
    }

    /**
     * 启用/停用用户
     */
    @PutMapping("/status/{id}")
    @SaCheckPermission("app:user:edit")
    @Operation(summary = "启用/停用用户")
    @Parameter(name = "id", description = "用户ID", required = true)
    public R<Void> changeStatus(@PathVariable Long id, @RequestParam Integer status) {
        appUserManageService.changeStatus(id, status);
        return R.ok();
    }
}
