package com.sd.admin.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.sd.admin.dto.LoginDTO;
import com.sd.admin.service.SysUserService;
import com.sd.admin.vo.CurrentUserVO;
import com.sd.admin.vo.LoginVO;
import com.sd.admin.vo.SysUserVO;
import com.sd.common.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 管理端认证控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "认证相关接口")
public class AuthController {

    private final SysUserService sysUserService;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    @Operation(summary = "管理员登录")
    public R<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        LoginVO loginVO = sysUserService.login(loginDTO);
        return R.ok("登录成功", loginVO);
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    @Operation(summary = "管理员登出")
    public R<Void> logout() {
        StpUtil.logout();
        return R.ok();
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取当前登录用户信息")
    public R<CurrentUserVO> getUserInfo() {
        Long userId = StpUtil.getLoginIdAsLong();
        CurrentUserVO user = sysUserService.getCurrentUserInfo(userId);
        return R.ok("获取用户信息成功", user);
    }
}
