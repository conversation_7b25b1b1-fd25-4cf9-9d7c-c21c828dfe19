import type { ThemeConfig } from 'antd';

const themeConfig: ThemeConfig = {
  token: {
    // 主色
    colorPrimary: '#1890ff',
    // 成功色
    colorSuccess: '#52c41a',
    // 警告色
    colorWarning: '#faad14',
    // 错误色
    colorError: '#ff4d4f',
    // 信息色
    colorInfo: '#1890ff',
    
    // 字体
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    fontSize: 14,
    
    // 圆角
    borderRadius: 6,
    
    // 线条
    lineWidth: 1,
    
    // 阴影
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    
    // 间距
    padding: 16,
    margin: 16,
    
    // 控件高度
    controlHeight: 32,
  },
  components: {
    // 按钮组件
    Button: {
      borderRadius: 6,
      controlHeight: 32,
    },
    // 输入框组件
    Input: {
      borderRadius: 6,
      controlHeight: 32,
    },
    // 选择器组件
    Select: {
      borderRadius: 6,
      controlHeight: 32,
    },
    // 表格组件
    Table: {
      borderRadius: 6,
      headerBg: '#fafafa',
    },
    // 卡片组件
    Card: {
      borderRadius: 8,
      headerBg: 'transparent',
    },
    // 菜单组件
    Menu: {
      borderRadius: 6,
      itemBorderRadius: 6,
    },
    // 布局组件
    Layout: {
      headerBg: '#fff',
      siderBg: '#001529',
      triggerBg: '#002140',
    },
  },
  algorithm: undefined, // 可以设置为 theme.darkAlgorithm 来启用暗色主题
};

export default themeConfig;
