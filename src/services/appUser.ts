import { get, post, put, del } from '../utils/request';

// 用户端用户查询参数
export interface AppUserQueryParams {
  pageNum: number;
  pageSize: number;
  username?: string;
  nickname?: string;
  phone?: string;
  email?: string;
  status?: number;
  inviteCode?: string;
}

// 用户端用户信息
export interface AppUserItem {
  id: number;
  username: string;
  nickname: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: number;
  birthday?: string;
  bio?: string;
  status: number;
  inviteCode: string;
  hasPayPassword: boolean;
  lastLoginTime?: string;
  lastLoginIp?: string;
  createTime: string;
  updateTime: string;
}

// 密码重置参数
export interface AppUserPasswordResetParams {
  id: number;
  newPassword: string;
}

// 分页查询用户端用户列表
export async function getAppUserPage(params: AppUserQueryParams) {
  return get<API.Response<API.PageResult<AppUserItem>>>('/app-user/page', params);
}

// 根据ID查询用户端用户详情
export async function getAppUserDetail(id: number) {
  return get<API.Response<AppUserItem>>(`/app-user/${id}`);
}

// 重置用户登录密码
export async function resetAppUserPassword(params: AppUserPasswordResetParams) {
  return put<API.Response<void>>('/app-user/reset-password', params);
}

// 重置用户支付密码
export async function resetAppUserPayPassword(params: AppUserPasswordResetParams) {
  return put<API.Response<void>>('/app-user/reset-pay-password', params);
}

// 启用/停用用户
export async function changeAppUserStatus(id: number, status: number) {
  return put<API.Response<void>>(`/app-user/status/${id}?status=${status}`);
}
