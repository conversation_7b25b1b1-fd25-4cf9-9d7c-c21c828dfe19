import { get, post, put, del, upload } from '../utils/request';

export interface RoleItem {
  id: number;
  roleName: string;
  roleKey: string;
  description?: string;
  sort: number;
  status: number;
  dataScope: number;
  remark?: string;
  createTime: string;
  updateTime: string;
  permissions?: PermissionItem[];
}

export interface PermissionItem {
  id: number;
  permissionName: string;
  permissionKey: string;
  type: number;
  parentId: number;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  isFrame: number;
  isCache: number;
  visible: number;
  status: number;
  remark?: string;
  createTime: string;
  updateTime: string;
  children?: PermissionItem[];
}

export interface RoleQueryParams {
  current?: number;
  pageSize?: number;
  roleName?: string;
  roleKey?: string;
  status?: number;
}

export interface RoleCreateParams {
  roleName: string;
  roleKey: string;
  description?: string;
  sort: number;
  status?: number;
  dataScope?: number;
  remark?: string;
  permissionIds?: number[];
}

export interface RoleUpdateParams extends RoleCreateParams {
  id: number;
}

export interface UserRoleAssignParams {
  userId: number;
  roleIds: number[];
}

// 分页查询角色列表
export async function getRolePage(params: RoleQueryParams) {
  return get<API.Response<API.PageResult<RoleItem>>>('/role/page', params);
}

// 查询所有角色列表
export async function getRoleList() {
  return get<API.Response<RoleItem[]>>('/role/list');
}

// 根据ID查询角色详情
export async function getRoleDetail(id: number) {
  return get<API.Response<RoleItem>>(`/role/${id}`);
}

// 创建角色
export async function createRole(data: RoleCreateParams) {
  return post<API.Response<number>>('/role', data);
}

// 更新角色
export async function updateRole(data: RoleUpdateParams) {
  return put<API.Response<void>>('/role', data);
}

// 删除角色
export async function deleteRole(id: number) {
  return del<API.Response<void>>(`/role/${id}`);
}

// 批量删除角色
export async function batchDeleteRole(ids: number[]) {
  return del<API.Response<void>>('/role/batch', { ids });
}

// 分配用户角色
export async function assignUserRoles(data: UserRoleAssignParams) {
  return post<API.Response<void>>('/user-role/assign', data);
}

// 根据用户ID查询角色ID列表
export async function getRoleIdsByUserId(userId: number) {
  return get<API.Response<number[]>>(`/user-role/user/${userId}`);
}

// 根据角色ID查询用户ID列表
export async function getUserIdsByRoleId(roleId: number) {
  return get<API.Response<number[]>>(`/user-role/role/${roleId}`);
}

// 导出默认对象
const roleService = {
  getRolePage,
  getRoleList,
  getRoleDetail,
  createRole,
  updateRole,
  deleteRole,
  batchDeleteRole,
  assignUserRoles,
  getRoleIdsByUserId,
  getUserIdsByRoleId,
};

export default roleService;
