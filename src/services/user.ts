import { get, post, put, del, upload } from '../utils/request';

export interface UserItem {
  id: number;
  username: string;
  nickname: string;
  email?: string;
  phone?: string;
  avatar?: string;
  status: number;
  createTime: string;
  updateTime: string;
  roles?: RoleItem[];
  roleNames?: string[];
}

export interface RoleItem {
  id: number;
  roleName: string;
  roleKey: string;
  description?: string;
  sort: number;
  status: number;
  dataScope: number;
  remark?: string;
}

export interface UserQueryParams {
  current?: number;
  pageSize?: number;
  username?: string;
  nickname?: string;
  email?: string;
  phone?: string;
  status?: number;
}

export interface UserCreateParams {
  username: string;
  password: string;
  nickname: string;
  email?: string;
  phone?: string;
  avatar?: string;
  status?: number;
  roleIds?: number[];
}

export interface UserUpdateParams {
  id: number;
  username: string;
  nickname: string;
  email?: string;
  phone?: string;
  avatar?: string;
  status?: number;
  roleIds?: number[];
}

export interface UserPasswordResetParams {
  id: number;
  newPassword: string;
}

// 分页查询用户列表
export async function getUserPage(params: UserQueryParams) {
  return get<API.Response<API.PageResult<UserItem>>>('/sys-user/page', params);
}

// 根据ID查询用户详情
export async function getUserDetail(id: number) {
  return get<API.Response<UserItem>>(`/sys-user/${id}`);
}

// 创建用户
export async function createUser(data: UserCreateParams) {
  return post<API.Response<number>>('/sys-user', data);
}

// 更新用户
export async function updateUser(data: UserUpdateParams) {
  return put<API.Response<void>>('/sys-user', data);
}

// 删除用户
export async function deleteUser(id: number) {
  return del<API.Response<void>>(`/sys-user/${id}`);
}

// 批量删除用户
export async function batchDeleteUser(ids: number[]) {
  return del<API.Response<void>>('/sys-user/batch', { ids });
}

// 重置用户密码
export async function resetUserPassword(data: UserPasswordResetParams) {
  return put<API.Response<void>>('/sys-user/reset-password', data);
}

// 获取当前用户信息
export async function getCurrentUser() {
  return get<API.Response<API.User>>('/sys-user/current');
}

// 导出默认对象
const userService = {
  getUserPage,
  getUserDetail,
  createUser,
  updateUser,
  deleteUser,
  batchDeleteUser,
  resetUserPassword,
  getCurrentUser,
};

export default userService;
