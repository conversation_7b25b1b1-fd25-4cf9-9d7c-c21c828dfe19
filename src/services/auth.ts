import { get, post, put, del, upload } from '../utils/request';

// 登录接口
export async function login(params: API.LoginParams): Promise<API.Response<API.LoginResult>> {
  return post('/auth/login', {
    username: params.username,
    password: params.password,
  });
}

// 退出登录
export async function logout(): Promise<API.Response<void>> {
  return post('/auth/logout');
}

// 获取当前用户信息
export async function getCurrentUser(): Promise<API.Response<API.User>> {
  return get('/auth/info');
}

// 修改密码
export async function changePassword(params: {
  oldPassword: string;
  newPassword: string;
}): Promise<API.Response<void>> {
  return post('/auth/change-password', params);
}

// 导出默认对象
const authService = {
  login,
  logout,
  getCurrentUser,
  changePassword,
};

export default authService;
