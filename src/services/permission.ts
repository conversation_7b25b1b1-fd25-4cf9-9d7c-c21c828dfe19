import { get, post, put, del, upload } from '../utils/request';

export interface PermissionItem {
  id: number;
  permissionName: string;
  permissionKey: string;
  type: number;
  parentId: number;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  isFrame: number;
  isCache: number;
  visible: number;
  status: number;
  remark?: string;
  createTime: string;
  updateTime: string;
  children?: PermissionItem[];
}

export interface PermissionCreateParams {
  permissionName: string;
  permissionKey: string;
  type: number;
  parentId?: number;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  isFrame?: number;
  isCache?: number;
  visible?: number;
  status?: number;
  remark?: string;
}

export interface PermissionUpdateParams extends PermissionCreateParams {
  id: number;
}

// 查询权限树
export async function getPermissionTree() {
  return get<API.Response<PermissionItem[]>>('/permission/tree');
}

// 根据ID查询权限详情
export async function getPermissionDetail(id: number) {
  return get<API.Response<PermissionItem>>(`/permission/${id}`);
}

// 创建权限
export async function createPermission(data: PermissionCreateParams) {
  return post<API.Response<number>>('/permission', data);
}

// 更新权限
export async function updatePermission(data: PermissionUpdateParams) {
  return put<API.Response<void>>('/permission', data);
}

// 删除权限
export async function deletePermission(id: number) {
  return del<API.Response<void>>(`/permission/${id}`);
}

// 导出默认对象
const permissionService = {
  getPermissionTree,
  getPermissionDetail,
  createPermission,
  updatePermission,
  deletePermission,
};

export default permissionService;
