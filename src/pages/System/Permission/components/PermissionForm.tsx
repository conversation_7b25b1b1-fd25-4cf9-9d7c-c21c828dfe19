import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, TreeSelect, message, Spin } from 'antd';
import type { PermissionItem, PermissionCreateParams, PermissionUpdateParams } from '@/services/permission';
import { createPermission, updatePermission, getPermissionTree } from '@/services/permission';

interface PermissionFormProps {
  visible: boolean;
  editingPermission?: PermissionItem;
  onCancel: () => void;
  onSubmit: () => void;
}

const PermissionForm: React.FC<PermissionFormProps> = ({
  visible,
  editingPermission,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [permissionTree, setPermissionTree] = useState<PermissionItem[]>([]);
  const [permissionType, setPermissionType] = useState<number>(1);

  useEffect(() => {
    if (visible) {
      loadPermissionTree();
      if (editingPermission) {
        form.setFieldsValue({
          permissionName: editingPermission.permissionName,
          permissionKey: editingPermission.permissionKey,
          type: editingPermission.type,
          parentId: editingPermission.parentId === 0 ? undefined : editingPermission.parentId,
          path: editingPermission.path,
          component: editingPermission.component,
          icon: editingPermission.icon,
          sort: editingPermission.sort,
          isFrame: editingPermission.isFrame,
          isCache: editingPermission.isCache,
          visible: editingPermission.visible,
          status: editingPermission.status,
          remark: editingPermission.remark,
        });
        setPermissionType(editingPermission.type);
      } else {
        form.resetFields();
        setPermissionType(1);
      }
    }
  }, [visible, editingPermission, form]);

  const loadPermissionTree = async () => {
    try {
      const response = await getPermissionTree();
      if (response.code === 200) {
        setPermissionTree(response.data || []);
      }
    } catch (error) {
      message.error('加载权限树失败');
    }
  };

  const convertToTreeSelectData = (permissions: PermissionItem[]) => {
    return permissions
      .filter(permission => permission.type !== 3) // 按钮不能作为父级
      .map(permission => ({
        title: permission.permissionName,
        value: permission.id,
        children: permission.children ? convertToTreeSelectData(permission.children) : undefined,
      }));
  };

  const handleTypeChange = (value: number) => {
    setPermissionType(value);
    // 如果是按钮类型，清空路由和组件相关字段
    if (value === 3) {
      form.setFieldsValue({
        path: undefined,
        component: undefined,
        icon: undefined,
        isFrame: 0,
        isCache: 0,
        visible: 0,
      });
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const params = {
        ...values,
        parentId: values.parentId || 0,
      };

      if (editingPermission) {
        const updateParams: PermissionUpdateParams = {
          id: editingPermission.id,
          ...params,
        };
        await updatePermission(updateParams);
        message.success('更新成功');
      } else {
        const createParams: PermissionCreateParams = params;
        await createPermission(createParams);
        message.success('创建成功');
      }

      onSubmit();
    } catch (error) {
      message.error(editingPermission ? '更新失败' : '创建失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingPermission ? '编辑权限' : '新建权限'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 1,
            status: 0,
            isFrame: 0,
            isCache: 0,
            visible: 0,
            sort: 0,
          }}
        >
          <Form.Item
            name="permissionName"
            label="权限名称"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input placeholder="请输入权限名称" />
          </Form.Item>

          <Form.Item
            name="permissionKey"
            label="权限标识"
            rules={[{ required: true, message: '请输入权限标识' }]}
          >
            <Input placeholder="请输入权限标识" />
          </Form.Item>

          <Form.Item
            name="type"
            label="权限类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Select placeholder="请选择权限类型" onChange={handleTypeChange}>
              <Select.Option value={1}>目录</Select.Option>
              <Select.Option value={2}>菜单</Select.Option>
              <Select.Option value={3}>按钮</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="parentId" label="上级权限">
            <TreeSelect
              placeholder="请选择上级权限"
              allowClear
              treeDefaultExpandAll
              treeData={[
                { title: '主类目', value: 0 },
                ...convertToTreeSelectData(permissionTree),
              ]}
            />
          </Form.Item>

          {permissionType !== 3 && (
            <>
              <Form.Item name="path" label="路由地址">
                <Input placeholder="请输入路由地址" />
              </Form.Item>

              <Form.Item name="component" label="组件路径">
                <Input placeholder="请输入组件路径" />
              </Form.Item>

              <Form.Item name="icon" label="图标">
                <Input placeholder="请输入图标" />
              </Form.Item>
            </>
          )}

          <Form.Item
            name="sort"
            label="显示顺序"
            rules={[{ required: true, message: '请输入显示顺序' }]}
          >
            <InputNumber min={0} placeholder="请输入显示顺序" style={{ width: '100%' }} />
          </Form.Item>

          {permissionType !== 3 && (
            <>
              <Form.Item name="isFrame" label="是否外链">
                <Select placeholder="请选择是否外链">
                  <Select.Option value={0}>否</Select.Option>
                  <Select.Option value={1}>是</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item name="isCache" label="是否缓存">
                <Select placeholder="请选择是否缓存">
                  <Select.Option value={0}>缓存</Select.Option>
                  <Select.Option value={1}>不缓存</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item name="visible" label="显示状态">
                <Select placeholder="请选择显示状态">
                  <Select.Option value={0}>显示</Select.Option>
                  <Select.Option value={1}>隐藏</Select.Option>
                </Select>
              </Form.Item>
            </>
          )}

          <Form.Item name="status" label="权限状态">
            <Select placeholder="请选择权限状态">
              <Select.Option value={0}>正常</Select.Option>
              <Select.Option value={1}>停用</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="remark" label="备注">
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default PermissionForm;
