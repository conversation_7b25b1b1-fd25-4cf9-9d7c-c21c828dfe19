import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Tag, Space } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useAccess, Access } from '@umijs/max';
import type { PermissionItem } from '@/services/permission';
import { getPermissionTree, deletePermission } from '@/services/permission';
import PermissionForm from './components/PermissionForm';

const PermissionList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState<PermissionItem | undefined>();

  // 使用 UmiJS 权限 Hook
  const access = useAccess();

  const columns: ProColumns<PermissionItem>[] = [
    {
      title: '权限名称',
      dataIndex: 'permissionName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '权限标识',
      dataIndex: 'permissionKey',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      valueEnum: {
        1: { text: '目录', status: 'Default' },
        2: { text: '菜单', status: 'Processing' },
        3: { text: '按钮', status: 'Success' },
      },
      render: (_, record) => {
        const typeMap = {
          1: { text: '目录', color: 'default' },
          2: { text: '菜单', color: 'blue' },
          3: { text: '按钮', color: 'green' },
        };
        const type = typeMap[record.type as keyof typeof typeMap];
        return <Tag color={type?.color}>{type?.text}</Tag>;
      },
    },
    {
      title: '图标',
      dataIndex: 'icon',
      width: 80,
      search: false,
      render: (text) => text ? <span className={`anticon anticon-${text}`} /> : '-',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
      search: false,
    },
    {
      title: '路由地址',
      dataIndex: 'path',
      width: 150,
      ellipsis: true,
      search: false,
    },
    {
      title: '组件路径',
      dataIndex: 'component',
      width: 200,
      ellipsis: true,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        0: { text: '正常', status: 'Success' },
        1: { text: '停用', status: 'Error' },
      },
      render: (_, record) => (
        <Tag color={record.status === 0 ? 'green' : 'red'}>
          {record.status === 0 ? '正常' : '停用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Access key="edit" accessible={access['system:permission:edit']}>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
        </Access>,
        <Access key="delete" accessible={access['system:permission:delete']}>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Access>,
      ],
    },
  ];

  const handleAdd = () => {
    setEditingPermission(undefined);
    setModalVisible(true);
  };

  const handleEdit = (record: PermissionItem) => {
    setEditingPermission(record);
    setModalVisible(true);
  };

  const handleDelete = (record: PermissionItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除权限"${record.permissionName}"吗？`,
      onOk: async () => {
        try {
          await deletePermission(record.id);
          message.success('删除成功');
          actionRef.current?.reload();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleFormSubmit = () => {
    setModalVisible(false);
    actionRef.current?.reload();
  };

  // 计算树形数据的总数量
  const countTreeNodes = (tree: PermissionItem[]): number => {
    let count = 0;
    const traverse = (nodes: PermissionItem[]) => {
      nodes.forEach(node => {
        count++;
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(tree);
    return count;
  };

  return (
    <PageContainer>
      <ProTable<PermissionItem>
        headerTitle="权限列表"
        actionRef={actionRef}
        rowKey="id"
        search={false}
        expandable={{
          childrenColumnName: 'children',
          defaultExpandAllRows: true,
        }}
        toolBarRender={() => [
          <Access key="add" accessible={access['system:permission:add']}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新建权限
            </Button>
          </Access>,
        ]}
        request={async () => {
          const response = await getPermissionTree();
          const treeData = response.data || [];
          return {
            data: treeData,
            success: response.code === 200,
            total: countTreeNodes(treeData),
          };
        }}
        columns={columns}
        pagination={false}
      />

      <PermissionForm
        visible={modalVisible}
        editingPermission={editingPermission}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleFormSubmit}
      />
    </PageContainer>
  );
};

export default PermissionList;
