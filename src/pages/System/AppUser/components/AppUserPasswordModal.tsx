import React, { useState } from 'react';
import { Modal, Form, Input, message } from 'antd';
import { resetAppUserPassword, resetAppUserPayPassword } from '@/services/appUser';
import type { AppUserItem } from '@/services/appUser';

interface AppUserPasswordModalProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  user?: AppUserItem;
  type: 'login' | 'pay'; // 密码类型：登录密码或支付密码
  onSuccess: () => void;
}

const AppUserPasswordModal: React.FC<AppUserPasswordModalProps> = ({
  visible,
  onVisibleChange,
  user,
  type,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (!user) {
        message.error('用户信息不存在');
        return;
      }

      const params = {
        id: user.id,
        newPassword: values.newPassword,
      };

      if (type === 'login') {
        await resetAppUserPassword(params);
      } else {
        await resetAppUserPayPassword(params);
      }

      onSuccess();
      onVisibleChange(false);
      form.resetFields();
    } catch (error) {
      message.error(`${type === 'login' ? '登录' : '支付'}密码重置失败`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onVisibleChange(false);
    form.resetFields();
  };

  return (
    <Modal
      title={`重置${type === 'login' ? '登录' : '支付'}密码`}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item label="用户信息">
          <div style={{ padding: '8px 12px', background: '#f5f5f5', borderRadius: '4px' }}>
            <div>用户名：{user?.username}</div>
            <div>昵称：{user?.nickname}</div>
            {user?.phone && <div>手机号：{user.phone}</div>}
          </div>
        </Form.Item>

        <Form.Item
          name="newPassword"
          label={`新${type === 'login' ? '登录' : '支付'}密码`}
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码至少6个字符' },
            { max: 20, message: '密码最多20个字符' },
          ]}
        >
          <Input.Password 
            placeholder={`请输入新${type === 'login' ? '登录' : '支付'}密码`}
            autoComplete="new-password"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password 
            placeholder="请再次输入密码"
            autoComplete="new-password"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AppUserPasswordModal;
