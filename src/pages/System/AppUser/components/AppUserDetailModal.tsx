import React from 'react';
import { Modal, Descriptions, Tag, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import type { AppUserItem } from '@/services/appUser';

interface AppUserDetailModalProps {
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  user?: AppUserItem;
}

const AppUserDetailModal: React.FC<AppUserDetailModalProps> = ({
  visible,
  onVisibleChange,
  user,
}) => {
  const handleCancel = () => {
    onVisibleChange(false);
  };

  const getGenderText = (gender?: number) => {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  };

  const getStatusTag = (status: number) => {
    return status === 0 ? (
      <Tag color="green">正常</Tag>
    ) : (
      <Tag color="red">停用</Tag>
    );
  };

  return (
    <Modal
      title="用户详情"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      {user && (
        <Descriptions bordered column={2}>
          <Descriptions.Item label="头像" span={2}>
            <Avatar 
              size={64} 
              src={user.avatar} 
              icon={<UserOutlined />}
            />
          </Descriptions.Item>
          
          <Descriptions.Item label="用户ID">
            {user.id}
          </Descriptions.Item>
          
          <Descriptions.Item label="用户名">
            {user.username}
          </Descriptions.Item>
          
          <Descriptions.Item label="昵称">
            {user.nickname}
          </Descriptions.Item>
          
          <Descriptions.Item label="邀请码">
            <Tag color="blue">{user.inviteCode}</Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="手机号">
            {user.phone || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="邮箱">
            {user.email || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="性别">
            {getGenderText(user.gender)}
          </Descriptions.Item>
          
          <Descriptions.Item label="生日">
            {user.birthday || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="状态">
            {getStatusTag(user.status)}
          </Descriptions.Item>
          
          <Descriptions.Item label="支付密码">
            <Tag color={user.hasPayPassword ? 'green' : 'red'}>
              {user.hasPayPassword ? '已设置' : '未设置'}
            </Tag>
          </Descriptions.Item>
          
          <Descriptions.Item label="个人简介" span={2}>
            {user.bio || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="最后登录时间">
            {user.lastLoginTime || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="最后登录IP">
            {user.lastLoginIp || '-'}
          </Descriptions.Item>
          
          <Descriptions.Item label="注册时间">
            {user.createTime}
          </Descriptions.Item>
          
          <Descriptions.Item label="更新时间">
            {user.updateTime}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );
};

export default AppUserDetailModal;
