import React, { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Popconfirm, message, Tag, Switch } from 'antd';
import { KeyOutlined, LockOutlined, EyeOutlined } from '@ant-design/icons';
import { useAccess, Access } from '@umijs/max';
import { getAppUserPage, changeAppUserStatus } from '@/services/appUser';
import type { AppUserItem } from '@/services/appUser';
import AppUserPasswordModal from './components/AppUserPasswordModal';
import AppUserDetailModal from './components/AppUserDetailModal';

const AppUserManagement: React.FC = () => {
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [payPasswordModalVisible, setPayPasswordModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<AppUserItem | undefined>();
  const actionRef = useRef<ActionType>();

  // 使用 UmiJS 权限 Hook
  const access = useAccess();

  // 处理状态切换
  const handleStatusChange = async (record: AppUserItem, checked: boolean) => {
    try {
      const status = checked ? 0 : 1;
      await changeAppUserStatus(record.id, status);
      message.success('状态修改成功');
      actionRef.current?.reload();
    } catch (error) {
      message.error('状态修改失败');
    }
  };

  const columns: ProColumns<AppUserItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
      ellipsis: true,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      width: 120,
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 130,
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: 180,
      ellipsis: true,
      search: false,
    },
    {
      title: '邀请码',
      dataIndex: 'inviteCode',
      width: 100,
      ellipsis: true,
    },
    {
      title: '支付密码',
      dataIndex: 'hasPayPassword',
      width: 100,
      search: false,
      render: (_, record) => (
        <Tag color={record.hasPayPassword ? 'green' : 'red'}>
          {record.hasPayPassword ? '已设置' : '未设置'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '正常', status: 'Success' },
        1: { text: '停用', status: 'Error' },
      },
      render: (_, record) => (
        <Access accessible={access['app:user:edit']}>
          <Switch
            checked={record.status === 0}
            onChange={(checked) => handleStatusChange(record, checked)}
            checkedChildren="正常"
            unCheckedChildren="停用"
          />
        </Access>
      ),
    },
    {
      title: '最后登录时间',
      dataIndex: 'lastLoginTime',
      width: 160,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '注册时间',
      dataIndex: 'createTime',
      width: 160,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      fixed: 'right',
      render: (_, record) => [
        <Access key="detail" accessible={access['app:user:view']}>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setDetailModalVisible(true);
            }}
          >
            详情
          </Button>
        </Access>,
        <Access key="resetPassword" accessible={access['app:user:reset:password']}>
          <Button
            type="link"
            size="small"
            icon={<LockOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setPasswordModalVisible(true);
            }}
          >
            重置密码
          </Button>
        </Access>,
        <Access key="resetPayPassword" accessible={access['app:user:reset:pay:password']}>
          <Button
            type="link"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setPayPasswordModalVisible(true);
            }}
          >
            重置支付密码
          </Button>
        </Access>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<AppUserItem>
        headerTitle="用户端用户列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        request={async (params, sort, filter) => {
          const response = await getAppUserPage({
            pageNum: params.current || 1,
            pageSize: params.pageSize || 10,
            username: params.username,
            nickname: params.nickname,
            phone: params.phone,
            email: params.email,
            status: params.status,
            inviteCode: params.inviteCode,
          });
          
          return {
            data: response.data?.list || [],
            success: response.code === 200,
            total: response.data?.total || 0,
          };
        }}
        columns={columns}
        scroll={{ x: 1200 }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      {/* 重置登录密码弹窗 */}
      <AppUserPasswordModal
        visible={passwordModalVisible}
        onVisibleChange={setPasswordModalVisible}
        user={currentRecord}
        type="login"
        onSuccess={() => {
          message.success('登录密码重置成功');
          actionRef.current?.reload();
        }}
      />

      {/* 重置支付密码弹窗 */}
      <AppUserPasswordModal
        visible={payPasswordModalVisible}
        onVisibleChange={setPayPasswordModalVisible}
        user={currentRecord}
        type="pay"
        onSuccess={() => {
          message.success('支付密码重置成功');
          actionRef.current?.reload();
        }}
      />

      {/* 用户详情弹窗 */}
      <AppUserDetailModal
        visible={detailModalVisible}
        onVisibleChange={setDetailModalVisible}
        user={currentRecord}
      />
    </PageContainer>
  );
};

export default AppUserManagement;
