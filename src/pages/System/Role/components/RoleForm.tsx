import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, Tree, message, Spin } from 'antd';
import type { RoleItem, RoleCreateParams, RoleUpdateParams } from '@/services/role';
import type { PermissionItem } from '@/services/permission';
import { createRole, updateRole } from '@/services/role';
import { getPermissionTree } from '@/services/permission';

interface RoleFormProps {
  visible: boolean;
  editingRole?: RoleItem;
  onCancel: () => void;
  onSubmit: () => void;
}

const RoleForm: React.FC<RoleFormProps> = ({
  visible,
  editingRole,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [permissionTree, setPermissionTree] = useState<PermissionItem[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    if (visible) {
      loadPermissionTree();
      if (editingRole) {
        form.setFieldsValue({
          roleName: editingRole.roleName,
          roleKey: editingRole.roleKey,
          description: editingRole.description,
          sort: editingRole.sort,
          status: editingRole.status,
          dataScope: editingRole.dataScope,
          remark: editingRole.remark,
        });
      } else {
        form.resetFields();
        setCheckedKeys([]);
      }
    }
  }, [visible, editingRole, form]);

  // 单独处理权限设置，确保权限树已加载
  useEffect(() => {
    if (visible && editingRole && permissionTree.length > 0) {
      // 设置已选择的权限（只选中叶子节点）
      if (editingRole.permissions) {
        const leafPermissionIds = getLeafPermissionIds(editingRole.permissions, permissionTree);
        setCheckedKeys(leafPermissionIds);
      }
    }
  }, [visible, editingRole, permissionTree]);

  const loadPermissionTree = async () => {
    try {
      const response = await getPermissionTree();
      if (response.code === 200) {
        setPermissionTree(response.data || []);
        // 默认展开所有节点
        const allKeys = getAllKeys(response.data || []);
        setExpandedKeys(allKeys);
      }
    } catch (error) {
      message.error('加载权限树失败');
    }
  };

  const getAllKeys = (tree: PermissionItem[]): React.Key[] => {
    const keys: React.Key[] = [];
    const traverse = (nodes: PermissionItem[]) => {
      nodes.forEach(node => {
        keys.push(node.id);
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(tree);
    return keys;
  };

  // 获取所有权限ID（包括子权限）
  const getAllPermissionIds = (permissions: PermissionItem[]): React.Key[] => {
    const ids: React.Key[] = [];
    const traverse = (nodes: PermissionItem[]) => {
      nodes.forEach(node => {
        ids.push(node.id);
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(permissions);
    return ids;
  };

  // 获取叶子节点权限ID（只选中叶子节点）
  const getLeafPermissionIds = (permissions: PermissionItem[], allPermissions: PermissionItem[]): React.Key[] => {
    const leafIds: React.Key[] = [];
    const allPermissionMap = new Map<number, PermissionItem>();

    // 构建所有权限的映射
    const buildPermissionMap = (nodes: PermissionItem[]) => {
      nodes.forEach(node => {
        allPermissionMap.set(node.id, node);
        if (node.children) {
          buildPermissionMap(node.children);
        }
      });
    };
    buildPermissionMap(allPermissions);

    // 查找叶子节点
    permissions.forEach(permission => {
      const permissionInTree = allPermissionMap.get(permission.id);
      if (permissionInTree && (!permissionInTree.children || permissionInTree.children.length === 0)) {
        leafIds.push(permission.id);
      }
    });

    return leafIds;
  };

  // 获取所有应该选中的权限ID（包括父节点）
  const getAllCheckedKeys = (checkedLeafKeys: React.Key[], allPermissions: PermissionItem[]): React.Key[] => {
    const allCheckedSet = new Set<React.Key>(checkedLeafKeys);
    const permissionMap = new Map<number, PermissionItem>();

    // 构建权限映射
    const buildPermissionMap = (nodes: PermissionItem[]) => {
      nodes.forEach(node => {
        permissionMap.set(node.id, node);
        if (node.children) {
          buildPermissionMap(node.children);
        }
      });
    };
    buildPermissionMap(allPermissions);

    // 递归添加父节点
    const addParentNodes = (nodeId: React.Key) => {
      const node = permissionMap.get(nodeId as number);
      if (node && node.parentId && node.parentId !== 0) {
        allCheckedSet.add(node.parentId);
        addParentNodes(node.parentId);
      }
    };

    // 为每个选中的节点添加其父节点
    checkedLeafKeys.forEach(key => {
      addParentNodes(key);
    });

    return Array.from(allCheckedSet);
  };

  const convertToTreeData = (permissions: PermissionItem[]) => {
    return permissions.map(permission => ({
      title: permission.permissionName,
      key: permission.id,
      children: permission.children ? convertToTreeData(permission.children) : undefined,
    }));
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 获取所有选中的权限ID（包括半选中的父节点）
      const allCheckedKeys = getAllCheckedKeys(checkedKeys, permissionTree);

      const params = {
        ...values,
        permissionIds: allCheckedKeys as number[],
      };

      if (editingRole) {
        const updateParams: RoleUpdateParams = {
          id: editingRole.id,
          ...params,
        };
        await updateRole(updateParams);
        message.success('更新成功');
      } else {
        const createParams: RoleCreateParams = params;
        await createRole(createParams);
        message.success('创建成功');
      }

      onSubmit();
    } catch (error) {
      message.error(editingRole ? '更新失败' : '创建失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingRole ? '编辑角色' : '新建角色'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 0,
            dataScope: 1,
            sort: 0,
          }}
        >
          <Form.Item
            name="roleName"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>

          <Form.Item
            name="roleKey"
            label="角色标识"
            rules={[{ required: true, message: '请输入角色标识' }]}
          >
            <Input placeholder="请输入角色标识" />
          </Form.Item>

          <Form.Item name="description" label="角色描述">
            <Input.TextArea placeholder="请输入角色描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="sort"
            label="显示顺序"
            rules={[{ required: true, message: '请输入显示顺序' }]}
          >
            <InputNumber min={0} placeholder="请输入显示顺序" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="status" label="状态">
            <Select placeholder="请选择状态">
              <Select.Option value={0}>正常</Select.Option>
              <Select.Option value={1}>停用</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="dataScope" label="数据范围">
            <Select placeholder="请选择数据范围">
              <Select.Option value={1}>全部数据权限</Select.Option>
              <Select.Option value={2}>自定数据权限</Select.Option>
              <Select.Option value={3}>本部门数据权限</Select.Option>
              <Select.Option value={4}>本部门及以下数据权限</Select.Option>
              <Select.Option value={5}>仅本人数据权限</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="remark" label="备注">
            <Input.TextArea placeholder="请输入备注" rows={3} />
          </Form.Item>

          <Form.Item label="权限分配">
            <Tree
              checkable
              checkedKeys={checkedKeys}
              expandedKeys={expandedKeys}
              onCheck={setCheckedKeys}
              onExpand={setExpandedKeys}
              treeData={convertToTreeData(permissionTree)}
              height={300}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default RoleForm;
