import React, { useRef, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tag } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useAccess, Access } from '@umijs/max';
import type { RoleItem, RoleQueryParams } from '@/services/role';
import { getRolePage, deleteRole, batchDeleteRole, getRoleDetail } from '@/services/role';
import RoleForm from './components/RoleForm';

const RoleList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleItem | undefined>();

  // 使用 UmiJS 权限 Hook
  const access = useAccess();

  const columns: ProColumns<RoleItem>[] = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '角色标识',
      dataIndex: 'roleKey',
      width: 150,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      ellipsis: true,
      search: false,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '正常', status: 'Success' },
        1: { text: '停用', status: 'Error' },
      },
      render: (_, record) => (
        <Tag color={record.status === 0 ? 'green' : 'red'}>
          {record.status === 0 ? '正常' : '停用'}
        </Tag>
      ),
    },
    {
      title: '数据范围',
      dataIndex: 'dataScope',
      width: 120,
      search: false,
      valueEnum: {
        1: '全部数据权限',
        2: '自定数据权限',
        3: '本部门数据权限',
        4: '本部门及以下数据权限',
        5: '仅本人数据权限',
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Access key="edit" accessible={access['system:role:edit']}>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
        </Access>,
        <Access key="delete" accessible={access['system:role:delete']}>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Access>,
      ],
    },
  ];

  const handleAdd = () => {
    setEditingRole(undefined);
    setModalVisible(true);
  };

  const handleEdit = async (record: RoleItem) => {
    try {
      // 获取角色详情（包含权限信息）
      const response = await getRoleDetail(record.id);
      if (response.code === 200) {
        setEditingRole(response.data);
        setModalVisible(true);
      } else {
        message.error('获取角色详情失败');
      }
    } catch (error) {
      message.error('获取角色详情失败');
    }
  };

  const handleDelete = (record: RoleItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除角色"${record.roleName}"吗？`,
      onOk: async () => {
        try {
          await deleteRole(record.id);
          message.success('删除成功');
          actionRef.current?.reload();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的角色');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个角色吗？`,
      onOk: async () => {
        try {
          await batchDeleteRole(selectedRowKeys as number[]);
          message.success('删除成功');
          setSelectedRowKeys([]);
          actionRef.current?.reload();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleFormSubmit = () => {
    setModalVisible(false);
    actionRef.current?.reload();
  };

  return (
    <PageContainer>
      <ProTable<RoleItem, RoleQueryParams>
        headerTitle="角色列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Access key="add" accessible={access['system:role:add']}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新建角色
            </Button>
          </Access>,
          <Access key="batchDelete" accessible={access['system:role:delete']}>
            <Button
              danger
              icon={<DeleteOutlined />}
              disabled={selectedRowKeys.length === 0}
              onClick={handleBatchDelete}
            >
              批量删除
            </Button>
          </Access>,
        ]}
        request={async (params) => {
          const response = await getRolePage(params);
          return {
            data: response.data?.list || [],
            success: response.code === 200,
            total: response.data?.total || 0,
          };
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
      />

      <RoleForm
        visible={modalVisible}
        editingRole={editingRole}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleFormSubmit}
      />
    </PageContainer>
  );
};

export default RoleList;
