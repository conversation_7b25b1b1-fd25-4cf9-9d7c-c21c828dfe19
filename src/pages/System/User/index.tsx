import React, { useState, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Popconfirm, message, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, KeyOutlined, TeamOutlined } from '@ant-design/icons';
import { useAccess, Access } from '@umijs/max';
import { getUserPage, deleteUser } from '@/services/user';
import UserForm from './components/UserForm';
import UserPasswordModal from './components/UserPasswordModal';
import UserRoleModal from './components/UserRoleModal';

export interface UserItem {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone: string;
  avatar: string;
  status: number;
  createTime: string;
  updateTime: string;
}

const UserManagement: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<UserItem | undefined>();
  const actionRef = useRef<ActionType>();

  // 使用 UmiJS 权限 Hook
  const access = useAccess();

  const columns: ProColumns<UserItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: 200,
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      search: false,
      render: (_, record) => (
        <Tag color={record.status === 0 ? 'green' : 'red'}>
          {record.status === 0 ? '正常' : '停用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Access key="edit" accessible={access['system:user:edit']}>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setUpdateModalVisible(true);
            }}
          >
            编辑
          </Button>
        </Access>,
        <Access key="password" accessible={access['system:user:resetPassword']}>
          <Button
            type="link"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setPasswordModalVisible(true);
            }}
          >
            重置密码
          </Button>
        </Access>,
        <Access key="role" accessible={access['system:user:edit']}>
          <Button
            type="link"
            size="small"
            icon={<TeamOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setRoleModalVisible(true);
            }}
          >
            分配角色
          </Button>
        </Access>,
        <Access key="delete" accessible={access['system:user:delete']}>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={async () => {
              try {
                await deleteUser(record.id);
                message.success('删除成功');
                actionRef.current?.reload();
              } catch (error) {
                message.error('删除失败');
              }
            }}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Access>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<UserItem>
        headerTitle="用户列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Access key="add" accessible={access['system:user:add']}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              新建用户
            </Button>
          </Access>,
        ]}
        request={async (params) => {
          const response = await getUserPage({
            current: params.current || 1,
            pageSize: params.pageSize || 10,
            username: params.username,
            nickname: params.nickname,
          });
          return {
            data: response.data.list,
            success: true,
            total: response.data.total,
          };
        }}
        columns={columns}
      />

      {/* 新建用户弹窗 */}
      <UserForm
        visible={createModalVisible}
        onVisibleChange={setCreateModalVisible}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />

      {/* 编辑用户弹窗 */}
      <UserForm
        visible={updateModalVisible}
        onVisibleChange={setUpdateModalVisible}
        initialValues={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />

      {/* 重置密码弹窗 */}
      <UserPasswordModal
        visible={passwordModalVisible}
        onVisibleChange={setPasswordModalVisible}
        user={currentRecord}
        onSuccess={() => {
          message.success('密码重置成功');
        }}
      />

      {/* 分配角色弹窗 */}
      <UserRoleModal
        visible={roleModalVisible}
        onVisibleChange={setRoleModalVisible}
        user={currentRecord}
        onSuccess={() => {
          message.success('角色分配成功');
        }}
      />
    </PageContainer>
  );
};

export default UserManagement;
