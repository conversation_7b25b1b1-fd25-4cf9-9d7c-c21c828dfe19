import React, { useState } from 'react';
import { Modal, Form, Input, message } from 'antd';
import { resetUserPassword } from '@/services/user';

interface UserPasswordModalProps {
  visible: boolean;
  userId?: number;
  userName?: string;
  onCancel: () => void;
  onSubmit: () => void;
}

const UserPasswordModal: React.FC<UserPasswordModalProps> = ({
  visible,
  userId,
  userName,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!userId) return;

    try {
      const values = await form.validateFields();
      setLoading(true);

      await resetUserPassword({
        id: userId,
        newPassword: values.newPassword,
      });

      message.success('密码重置成功');
      form.resetFields();
      onSubmit();
    } catch (error) {
      message.error('密码重置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`重置用户"${userName}"的密码`}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="newPassword"
          label="新密码"
          rules={[
            { required: true, message: '请输入新密码' },
            { min: 6, message: '密码长度不能少于6位' }
          ]}
        >
          <Input.Password placeholder="请输入新密码" />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认密码"
          dependencies={['newPassword']}
          rules={[
            { required: true, message: '请确认新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password placeholder="请确认新密码" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserPasswordModal;
