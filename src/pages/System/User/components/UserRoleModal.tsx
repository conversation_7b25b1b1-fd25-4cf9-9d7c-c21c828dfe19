import React, { useEffect, useState } from 'react';
import { Modal, Transfer, message, Spin } from 'antd';
import type { TransferDirection } from 'antd/es/transfer';
import { getRoleList, assignUserRoles, getRoleIdsByUserId } from '@/services/role';
import type { RoleItem } from '@/services/role';

interface UserRoleModalProps {
  visible: boolean;
  userId?: number;
  userName?: string;
  onCancel: () => void;
  onSubmit: () => void;
}

interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const UserRoleModal: React.FC<UserRoleModalProps> = ({
  visible,
  userId,
  userName,
  onCancel,
  onSubmit,
}) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<TransferItem[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  useEffect(() => {
    if (visible && userId) {
      loadData();
    }
  }, [visible, userId]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 加载所有角色
      const roleResponse = await getRoleList();
      if (roleResponse.code === 200) {
        const roles = roleResponse.data || [];
        const transferData = roles.map((role: RoleItem) => ({
          key: role.id.toString(),
          title: role.roleName,
          description: role.description,
        }));
        setDataSource(transferData);

        // 加载用户已分配的角色
        const userRoleResponse = await getRoleIdsByUserId(userId!);
        if (userRoleResponse.code === 200) {
          const roleIds = userRoleResponse.data || [];
          setTargetKeys(roleIds.map(id => id.toString()));
        }
      }
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (newTargetKeys: string[], direction: TransferDirection, moveKeys: string[]) => {
    setTargetKeys(newTargetKeys);
  };

  const handleSubmit = async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const roleIds = targetKeys.map(key => parseInt(key, 10));
      await assignUserRoles({
        userId,
        roleIds,
      });
      message.success('角色分配成功');
      onSubmit();
    } catch (error) {
      message.error('角色分配失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`为用户"${userName}"分配角色`}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Transfer
          dataSource={dataSource}
          titles={['可选角色', '已选角色']}
          targetKeys={targetKeys}
          onChange={handleChange}
          render={item => item.title}
          listStyle={{
            width: 350,
            height: 400,
          }}
          showSearch
          filterOption={(inputValue, option) =>
            option.title.indexOf(inputValue) > -1
          }
        />
      </Spin>
    </Modal>
  );
};

export default UserRoleModal;
