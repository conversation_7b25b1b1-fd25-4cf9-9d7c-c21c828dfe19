import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Transfer, message, Spin } from 'antd';
import type { UserItem, UserCreateParams, UserUpdateParams } from '@/services/user';
import type { RoleItem } from '@/services/role';
import { createUser, updateUser, getUserDetail } from '@/services/user';
import { getRoleList } from '@/services/role';

interface UserFormProps {
  visible: boolean;
  editingUser?: UserItem;
  onCancel: () => void;
  onSubmit: () => void;
}

interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const UserForm: React.FC<UserFormProps> = ({
  visible,
  editingUser,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [roleDataSource, setRoleDataSource] = useState<TransferItem[]>([]);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  useEffect(() => {
    if (visible) {
      loadRoles();
      if (editingUser) {
        loadUserDetail();
      } else {
        form.resetFields();
        setTargetKeys([]);
      }
    }
  }, [visible, editingUser, form]);

  const loadRoles = async () => {
    try {
      const response = await getRoleList();
      if (response.code === 200) {
        const roles = response.data || [];
        const transferData = roles.map((role: RoleItem) => ({
          key: role.id.toString(),
          title: role.roleName,
          description: role.description,
        }));
        setRoleDataSource(transferData);
      }
    } catch (error) {
      message.error('加载角色列表失败');
    }
  };

  const loadUserDetail = async () => {
    if (!editingUser) return;
    
    try {
      const response = await getUserDetail(editingUser.id);
      if (response.code === 200) {
        const user = response.data;
        form.setFieldsValue({
          username: user.username,
          nickname: user.nickname,
          email: user.email,
          phone: user.phone,
          avatar: user.avatar,
          status: user.status,
        });
        
        // 设置已选择的角色
        if (user.roles) {
          const roleIds = user.roles.map(role => role.id.toString());
          setTargetKeys(roleIds);
        }
      }
    } catch (error) {
      message.error('加载用户详情失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const roleIds = targetKeys.map(key => parseInt(key, 10));
      const params = {
        ...values,
        roleIds,
      };

      if (editingUser) {
        const updateParams: UserUpdateParams = {
          id: editingUser.id,
          ...params,
        };
        await updateUser(updateParams);
        message.success('更新成功');
      } else {
        const createParams: UserCreateParams = params;
        await createUser(createParams);
        message.success('创建成功');
      }

      onSubmit();
    } catch (error) {
      message.error(editingUser ? '更新失败' : '创建失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingUser ? '编辑用户' : '新建用户'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 0,
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: '请输入昵称' }]}
          >
            <Input placeholder="请输入昵称" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { type: 'email', message: '请输入正确的邮箱格式' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' }
            ]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item name="avatar" label="头像">
            <Input placeholder="请输入头像URL" />
          </Form.Item>

          <Form.Item name="status" label="状态">
            <Select placeholder="请选择状态">
              <Select.Option value={0}>正常</Select.Option>
              <Select.Option value={1}>停用</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="角色分配">
            <Transfer
              dataSource={roleDataSource}
              titles={['可选角色', '已选角色']}
              targetKeys={targetKeys}
              onChange={setTargetKeys}
              render={item => item.title}
              listStyle={{
                width: 300,
                height: 300,
              }}
              showSearch
              filterOption={(inputValue, option) =>
                option.title.indexOf(inputValue) > -1
              }
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default UserForm;
