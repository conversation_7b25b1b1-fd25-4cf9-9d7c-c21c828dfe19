import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Checkbox } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { login } from '@/services/auth';
import styles from './login.less';

interface LoginForm {
  username: string;
  password: string;
  remember?: boolean;
}

const LoginPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { initialState, setInitialState } = useModel('@@initialState');

  const handleSubmit = async (values: LoginForm) => {
    setLoading(true);
    try {
      console.log('开始登录，发送参数:', values);
      const response = await login({
        username: values.username,
        password: values.password,
      });

      console.log('登录响应:', response);
      console.log('响应类型:', typeof response);
      console.log('响应code:', response?.code);
      console.log('响应code类型:', typeof response?.code);
      console.log('响应data:', response?.data);
      console.log('响应message:', response?.message);

      if (response && response.code === 200) {
        console.log('登录成功，准备保存token');

        // 保存token到localStorage
        localStorage.setItem('token', response.data.token);
        console.log('Token已保存:', response.data.token);

        // 更新全局状态
        if (setInitialState) {
          console.log('更新全局状态');
          await setInitialState((s) => ({
            ...s,
            currentUser: {
              user: response.data.user,
              username: response.data.user.username,
              nickname: response.data.user.nickname,
              avatar: response.data.user.avatar,
            },
          }));
          console.log('全局状态更新完成');
        }

        // 显示成功消息
        message.success('登录成功');

        // 稍微延迟跳转，确保状态更新完成
        setTimeout(() => {
          console.log('开始跳转到首页');
          history.push('/');
        }, 300); // 增加延迟时间
      } else {
        console.log('登录失败，code不等于200:', response?.code);
        message.error(response?.message || '登录失败');
      }
    } catch (error) {
      console.error('登录异常:', error);
      message.error('登录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.top}>
          <div className={styles.header}>
            <span className={styles.title}>管理后台</span>
          </div>
          <div className={styles.desc}>基于 UmiJS + Ant Design 的管理系统</div>
        </div>
        <div className={styles.main}>
          <Card className={styles.loginCard}>
            <Form
              name="login"
              size="large"
              onFinish={handleSubmit}
              autoComplete="off"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名!' },
                  { min: 3, message: '用户名至少3个字符!' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码!' },
                  { min: 6, message: '密码至少6个字符!' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                />
              </Form.Item>

              <Form.Item>
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>记住我</Checkbox>
                </Form.Item>
                <a className={styles.forgot} href="">
                  忘记密码
                </a>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                >
                  登录
                </Button>
              </Form.Item>

              <div style={{ textAlign: 'center', marginTop: 16, color: '#666' }}>
                <p>测试账号：</p>
                <p>用户名：admin</p>
                <p>密码：123456</p>
              </div>
            </Form>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
