import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Toast, Radio, TextArea } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { getUserInfo, updateUserInfo } from '../../services/auth';
import type { UserInfo } from '../../services/auth';
import './EditProfile.css';

const EditProfilePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const response = await getUserInfo();
      if (response && response.code === 200) {
        setUserInfo(response.data);
      }
    } catch (error) {
      Toast.show('获取用户信息失败');
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const response = await updateUserInfo({
        nickname: values.nickname,
        email: values.email,
        gender: values.gender,
        birthday: values.birthday,
        bio: values.bio,
      });

      if (response && response.code === 200) {
        Toast.show('个人信息更新成功');
        navigate('/profile');
      } else {
        Toast.show(response?.msg || '更新失败');
      }
    } catch (error) {
      console.error('更新异常:', error);
      Toast.show('更新失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  if (!userInfo) {
    return <div className="loading">加载中...</div>;
  }

  return (
    <div className="edit-profile-container">
      <div className="edit-profile-header">
        <Button
          fill="none"
          onClick={() => navigate('/profile')}
        >
          ← 返回
        </Button>
        <h1>编辑个人信息</h1>
      </div>

      <div className="edit-profile-form">
        <Form
          initialValues={{
            nickname: userInfo.nickname,
            email: userInfo.email,
            gender: userInfo.gender,
            birthday: userInfo.birthday,
            bio: userInfo.bio,
          }}
          onFinish={handleSubmit}
          footer={
            <Button
              block
              type="submit"
              color="primary"
              loading={loading}
              size="large"
            >
              保存
            </Button>
          }
        >
          <Form.Item label="用户名">
            <Input
              value={userInfo.username}
              disabled
            />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[
              { required: true, message: '请输入昵称' },
              { min: 2, message: '昵称至少2个字符' },
              { max: 20, message: '昵称最多20个字符' },
            ]}
          >
            <Input
              placeholder="请输入昵称"
            />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { type: 'email', message: '请输入正确的邮箱地址' },
            ]}
          >
            <Input
              placeholder="请输入邮箱"
            />
          </Form.Item>

          <Form.Item
            name="gender"
            label="性别"
          >
            <Radio.Group>
              <Radio value={0}>未知</Radio>
              <Radio value={1}>男</Radio>
              <Radio value={2}>女</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="birthday"
            label="生日"
          >
            <Input
              placeholder="请输入生日 (YYYY-MM-DD)"
            />
          </Form.Item>

          <Form.Item
            name="bio"
            label="个人简介"
          >
            <TextArea
              placeholder="请输入个人简介"
              maxLength={200}
              rows={3}
              showCount
            />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditProfilePage;
