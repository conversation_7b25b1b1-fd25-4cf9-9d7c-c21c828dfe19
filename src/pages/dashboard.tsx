import React from 'react';
import { <PERSON>, Row, Col, Statistic, Typography, Space, Button } from 'antd';
import { UserOutlined, TeamOutlined, SafetyOutlined, SettingOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';

const { Title, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  return (
    <PageContainer
      title="仪表盘"
      subTitle="欢迎使用管理后台系统"
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={1128}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="角色数量"
              value={8}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="权限数量"
              value={32}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统模块"
              value={5}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="系统概览" bordered={false}>
            <Title level={4}>管理后台系统</Title>
            <Paragraph>
              这是一个基于 UmiJS 4 + Ant Design 5 + TypeScript 构建的现代化管理后台系统。
              系统采用最新的前端技术栈，提供了完整的用户管理、角色管理、权限管理等功能。
            </Paragraph>
            <Title level={5}>主要特性：</Title>
            <ul>
              <li>🚀 基于 UmiJS 4 和 Ant Design 5 的最新技术栈</li>
              <li>📱 响应式设计，支持移动端和桌面端</li>
              <li>🔐 完整的权限管理系统</li>
              <li>🎨 现代化的 UI 设计</li>
              <li>⚡ 快速的开发体验</li>
              <li>🛡️ TypeScript 类型安全</li>
            </ul>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="快速操作" bordered={false}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" block icon={<UserOutlined />}>
                用户管理
              </Button>
              <Button block icon={<TeamOutlined />}>
                角色管理
              </Button>
              <Button block icon={<SafetyOutlined />}>
                权限管理
              </Button>
              <Button block icon={<SettingOutlined />}>
                系统设置
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Dashboard;
