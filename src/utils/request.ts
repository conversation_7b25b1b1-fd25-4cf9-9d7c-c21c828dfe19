import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { message, notification } from 'antd';
import { history } from '@umijs/max';

// 创建 axios 实例
const instance = axios.create({
  baseURL: '/api', // 统一添加前缀
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    console.log('Request interceptor called for URL:', config.url);

    // 如果是SSE请求，不进行拦截
    if (config.url?.includes('/sse/logs/') && config.headers?.Accept === 'text/event-stream') {
      console.log('SSE request detected, skipping interceptor');
      return config;
    }

    // 从 localStorage 获取 token
    const token = localStorage.getItem('token');
    console.log('Token in request interceptor:', token ? 'exists' : 'not found');
    // 如果有 token，则添加到请求头
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log('Added token to request headers');
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('Response interceptor called for URL:', response.config.url);
    console.log('Response status:', response.status);

    // 如果是SSE响应，不进行拦截
    if (response.headers['content-type']?.includes('text/event-stream')) {
      console.log('SSE response detected, skipping interceptor');
      return response;
    }

    const { data } = response;
    console.log('Response data:', data);

    // 如果后端返回的是自定义格式的数据（包含code字段）
    if (data.hasOwnProperty('code')) {
      console.log('Response has code property');
      // 如果请求成功（code为200到2xx）
      if (data.code >= 200 && data.code < 300) {
        console.log('Request successful, returning data directly');
        return data;
      } else {
        // 业务错误处理
        console.error('Business error:', data.message);
        message.error(data.message || '操作失败');
        return Promise.reject(new Error(data.message || '操作失败'));
      }
    }

    // 如果是普通响应，直接返回数据
    console.log('Regular response, returning data directly');
    return data;
  },
  (error: AxiosError) => {
    console.error('Response error:', error);
    const { response } = error;

    if (response) {
      const { status, data } = response as any;
      console.error(`Error status: ${status}, data:`, data);

      // 根据状态码处理不同的错误
      switch (status) {
        case 400:
          message.error(data.message || '请求参数错误');
          break;
        case 401:
          // 未登录或 token 过期
          console.error('401 Unauthorized error');

          // 检查当前页面是否已经是登录页
          if (window.location.pathname === '/login') {
            console.log('Already on login page, not redirecting');
            // 已经在登录页面，不再重定向或显示错误提示
            // 返回空对象而不是拒绝Promise，避免循环请求
            return {};
          }

          // 清除本地 token 和用户信息
          localStorage.removeItem('token');
          localStorage.removeItem('remember');

          // 显示错误提示
          message.error('登录已过期，请重新登录');

          // 跳转到登录页
          setTimeout(() => {
            console.log('Redirecting to login page');
            window.location.href = '/login';
          }, 1000);

          // 返回空对象而不是拒绝Promise，避免循环请求
          return {};
          break;
        case 403:
          message.error('没有权限访问该资源');
          // 返回空数据而不是拒绝Promise
          return {};
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          notification.error({
            message: '服务器错误',
            description: data.message || '服务器发生错误，请联系管理员',
          });
          break;
        default:
          message.error(data.message || `请求错误: ${status}`);
      }
    } else {
      // 网络错误或请求被取消
      if (error.message.includes('timeout')) {
        message.error('请求超时，请检查网络连接');
      } else {
        message.error('网络异常，无法连接服务器');
      }
    }

    return Promise.reject(error);
  }
);

// 封装 GET 请求
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.get(url, { params, ...config });
}

// 封装 POST 请求
export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  console.log(`发送POST请求到 ${url}`, data);
  return instance.post(url, data, config);
}

// 封装 PUT 请求
export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.put(url, data, config);
}

// 封装 DELETE 请求
export function del<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.delete(url, { params, ...config });
}

// 封装上传文件的请求
export function upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
  return instance.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  });
}

// 导出默认实例，以便可以直接使用其他方法
export default instance;
