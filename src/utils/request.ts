import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse } from '../types';

// 创建 axios 实例
const instance = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:8081' : '', // 用户端后端地址
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config: any) => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token');
    // 如果有 token，则添加到请求头
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;

    // 如果后端返回的是自定义格式的数据（包含code字段）
    if (data.hasOwnProperty('code')) {
      // 如果请求成功（code为200到2xx）
      if (data.code >= 200 && data.code < 300) {
        // 返回修改后的response，将data字段替换为实际数据
        return {
          ...response,
          data: data.data
        };
      } else {
        // 业务错误处理
        console.error('Business error:', data.msg);
        return Promise.reject(new Error(data.msg || '操作失败'));
      }
    }

    // 如果是普通响应，直接返回原始response
    return response;
  },
  (error: AxiosError) => {
    const { response } = error;

    if (response) {
      const { status, data } = response as AxiosResponse<ApiResponse>;

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          console.error('没有权限访问该资源');
          break;
        case 404:
          console.error('请求的资源不存在');
          break;
        case 500:
          console.error('服务器错误:', data?.msg || '服务器发生错误');
          break;
        default:
          console.error(`请求错误: ${status}`, data?.msg);
      }
    } else {
      // 网络错误或请求被取消
      if (error.message.includes('timeout')) {
        console.error('请求超时，请检查网络连接');
      } else {
        console.error('网络异常，无法连接服务器');
      }
    }

    return Promise.reject(error);
  }
);

// 封装 GET 请求
export function get<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.get(url, { params, ...config }).then(response => response.data);
}

// 封装 POST 请求
export function post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.post(url, data, config).then(response => response.data);
}

// 封装 PUT 请求
export function put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.put(url, data, config).then(response => response.data);
}

// 封装 DELETE 请求
export function del<T = any>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return instance.delete(url, { params, ...config }).then(response => response.data);
}

// 封装上传文件的请求
export function upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
  return instance.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  }).then(response => response.data);
}
