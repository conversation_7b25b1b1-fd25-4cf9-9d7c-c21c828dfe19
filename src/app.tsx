import {message, ConfigProvider, Spin, Space} from 'antd';
import themeConfig from './theme';
import AvatarDropdown from '@/components/RightContent/AvatarDropdown';
import { getCurrentUser } from '@/services/auth';
import {Link, history} from '@umijs/max';
import type { RunTimeLayoutConfig } from '@umijs/max';

// 全局主题配置
export function rootContainer(container: React.ReactNode) {
  return (
      <ConfigProvider theme={themeConfig}>
          {container}
      </ConfigProvider>
  );
}

// 运行时配置
export const layout: RunTimeLayoutConfig = ({initialState}) => {
  console.log('Layout config called with initialState:', initialState);

  return {
    waterMarkProps: {
      content: initialState?.currentUser?.user?.username || initialState?.currentUser?.username || '',
    },
    onPageChange: () => {
      const { location } = history;
      const {pathname} = location;

      // 登录页面不需要权限控制
      if (pathname === '/login') {
        return;
      }

      // 获取当前用户信息
      const token = localStorage.getItem('token');

      // @ts-ignore
      const currentUser = initialState?.currentUser;

      // 如果没有 token，重定向到登录页面
      if (!token) {
        localStorage.removeItem('token');
        window.location.href = `/login?redirect=${encodeURIComponent(pathname)}`;
        return;
      }

      // 如果有 token 但没有 currentUser，可能是初始化过程中，允许访问
      // initialState 会在后台异步加载用户信息
      return; // 允许访问
    },
    rightContentRender: () => (
        <Space>
          <AvatarDropdown />
        </Space>
    ),
    footerRender: () => (
        <div style={{textAlign: 'center', padding: '16px'}}>
          Cloudflare域名管理系统 ©2024 Created by Garen
        </div>
    ),
  };
};

// 初始化状态
export async function getInitialState(): Promise<{
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  console.log('getInitialState called');

  const fetchUserInfo = async () => {
    try {
      // 检查本地存储中是否有token
      const token = localStorage.getItem('token');

      if (!token) {
        return undefined;
      }

      // 直接调用后端 API 获取最新的用户信息
      const response = await getCurrentUser();
      if (response && response.code === 200) {
        const currentUser = {
          user: response.data,
          username: response.data.username,
          nickname: response.data.nickname,
          avatar: response.data.avatar,
          // 直接将角色和权限信息放在顶层，供 access.ts 使用
          roles: response.data.roles,
          email: response.data.email,
          id: response.data.id,
        };
        return currentUser;
      }
      return undefined;
    } catch (error) {
      console.error('获取用户信息失败', error);
      // 如果获取用户信息失败，可能是token过期，清除token
      localStorage.removeItem('token');
      return undefined;
    }
  };

  // 如果是登录页面，不获取用户信息
  if (window.location.pathname === '/login') {
    return {
      fetchUserInfo,
    };
  }

  const currentUser = await fetchUserInfo();

  return {
    fetchUserInfo,
    currentUser, // 即使为 undefined 也返回，让 onPageChange 处理权限控制
  };
}
