import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { Toast } from 'antd-mobile';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('token');
      if (token) {
        setIsAuthenticated(true);
      } else {
        setIsAuthenticated(false);
        Toast.show('请先登录');
      }
    };

    checkAuth();
  }, []);

  if (isAuthenticated === null) {
    // 正在检查认证状态
    return <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh'
    }}>加载中...</div>;
  }

  if (!isAuthenticated) {
    // 未认证，重定向到登录页
    return <Navigate to="/login" replace />;
  }

  // 已认证，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
