import React from 'react';
import { Avatar, Dropdown, Space, message } from 'antd';
import { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';

const AvatarDropdown: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');

  const handleLogout = () => {
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('remember');
    
    // 清除全局状态
    setInitialState((s) => ({ ...s, currentUser: undefined }));
    
    // 跳转到登录页
    history.push('/login');
    message.success('退出登录成功');
  };

  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      case 'profile':
        // 跳转到个人中心
        message.info('个人中心功能开发中');
        break;
      case 'settings':
        // 跳转到个人设置
        message.info('个人设置功能开发中');
        break;
      case 'logout':
        handleLogout();
        break;
      default:
        break;
    }
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '个人设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const currentUser = initialState?.currentUser;

  if (!currentUser) {
    return null;
  }

  return (
    <Dropdown
      menu={{ items: menuItems, onClick: handleMenuClick }}
      placement="bottomRight"
      arrow
    >
      <Space style={{ cursor: 'pointer', padding: '0 8px' }}>
        <Avatar size="small" icon={<UserOutlined />} />
        <span>{currentUser?.user?.nickname || currentUser?.user?.username || currentUser?.username || '用户'}</span>
      </Space>
    </Dropdown>
  );
};

export default AvatarDropdown;
