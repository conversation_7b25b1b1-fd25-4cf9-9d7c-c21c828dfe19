import React from 'react';
import { SafeArea } from 'antd-mobile';
import CustomTabBar from '../TabBar';
import { tabItems } from '../../config/navigation';
import './index.css';

interface LayoutProps {
  children: React.ReactNode;
  showTabBar?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ children, showTabBar = true }) => {
  return (
    <div className="app-layout">
      <div className="layout-content">
        <SafeArea position="top" />
        {children}
        <div className="layout-bottom-spacer" />
        <SafeArea position="bottom" />
      </div>
      
      {showTabBar && (
        <CustomTabBar tabs={tabItems} />
      )}
    </div>
  );
};

export default Layout;
