package com.sd.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 用户登录DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "用户登录参数")
public class LoginDTO {

    @NotBlank(message = "登录类型不能为空")
    @Schema(description = "登录类型（username/phone）", required = true)
    private String loginType;

    @NotBlank(message = "登录值不能为空")
    @Schema(description = "登录值（用户名或手机号）", required = true)
    private String loginValue;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码", required = true)
    private String password;
}
