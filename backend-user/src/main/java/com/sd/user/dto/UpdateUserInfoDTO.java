package com.sd.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

/**
 * 更新用户信息DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Schema(description = "更新用户信息参数")
public class UpdateUserInfoDTO {

    @Size(min = 2, max = 20, message = "昵称长度必须在2-20个字符之间")
    @Schema(description = "昵称")
    private String nickname;

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别（0未知 1男 2女）")
    private Integer gender;

    @Schema(description = "生日")
    private String birthday;

    @Size(max = 200, message = "个人简介最多200个字符")
    @Schema(description = "个人简介")
    private String bio;
}
