package com.sd.user.controller;

import com.sd.common.api.R;
import com.sd.user.dto.*;
import com.sd.user.service.AppUserService;
import com.sd.user.vo.LoginVO;
import com.sd.user.vo.UserInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;



/**
 * 用户端认证控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "用户端认证", description = "用户端认证相关接口")
public class AuthController {

    private final AppUserService appUserService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public R<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        LoginVO loginVO = appUserService.login(loginDTO);
        return R.ok("登录成功", loginVO);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public R<Void> register(@Valid @RequestBody RegisterDTO registerDTO) {
        appUserService.register(registerDTO);
        return R.ok();
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出")
    public R<Void> logout() {
        appUserService.logout();
        return R.ok();
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/userinfo")
    @Operation(summary = "获取当前登录用户信息")
    public R<UserInfoVO> getUserInfo() {
        UserInfoVO userInfoVO = appUserService.getCurrentUserInfo();
        return R.ok("获取用户信息成功", userInfoVO);
    }



    /**
     * 修改密码
     */
    @PostMapping("/changePassword")
    @Operation(summary = "修改密码")
    public R<Void> changePassword(@Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        appUserService.changePassword(changePasswordDTO);
        return R.ok();
    }

    /**
     * 设置支付密码（首次设置）
     */
    @PostMapping("/setPayPassword")
    @Operation(summary = "设置支付密码")
    public R<Void> setPayPassword(@Valid @RequestBody SetPayPasswordDTO setPayPasswordDTO) {
        appUserService.setPayPassword(setPayPasswordDTO);
        return R.ok();
    }

    /**
     * 修改支付密码
     */
    @PostMapping("/changePayPassword")
    @Operation(summary = "修改支付密码")
    public R<Void> changePayPassword(@Valid @RequestBody ChangePayPasswordDTO changePayPasswordDTO) {
        appUserService.changePayPassword(changePayPasswordDTO);
        return R.ok();
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/updateUserInfo")
    @Operation(summary = "更新用户信息")
    public R<Void> updateUserInfo(@Valid @RequestBody UpdateUserInfoDTO updateUserInfoDTO) {
        appUserService.updateUserInfo(updateUserInfoDTO);
        return R.ok();
    }

}
